import numpy as np
import pandas as pd
from scipy import stats
import talib as ta
import statsmodels.api as sm

class StockOldAnalyzer:
    """股票分析器类，提供多种技术分析和量化指标计算功能（优化版）"""
    def __init__(self, data, config=None):
        """初始化StockOldAnalyzer实例

        Args:
            data (pd.DataFrame): 股票数据DataFrame
            config (dict, optional): 配置字典，可指定列名等参数
        """
        self.data = data
        self.config = config or {}
        self.date_column = self.config.get('date_column', 'date')
        self.close_column = self.config.get('close_column', 'close')
        self.high_column = self.config.get('high_column', 'high')
        self.low_column = self.config.get('low_column', 'low')
        self.volume_column = self.config.get('volume_column', 'volume')
        self.open_column = self.config.get('open_column', 'open')
        self.amount_column = self.config.get('amount_column', 'amount')
        self.fast_calculator = None

        # 预计算常用指标，避免重复计算
        if self.data is not None and not self.data.empty:
            self._pre_calculate_indicators()

    def _safe_iloc(self, index):
        """安全的iloc访问，避免索引越界"""
        try:
            if self.data is None or self.data.empty:
                return None
            if isinstance(index, int):
                if index < 0:
                    # 负索引
                    if abs(index) > len(self.data):
                        return None
                else:
                    # 正索引
                    if index >= len(self.data):
                        return None
                return self.data.iloc[index]
            return None
        except (IndexError, KeyError):
            return None

    def _safe_iloc_range(self, start_idx, end_idx=None):
        """安全的iloc范围访问"""
        try:
            if self.data is None or self.data.empty:
                return pd.DataFrame()

            data_len = len(self.data)

            # 处理负索引
            if start_idx < 0:
                start_idx = max(0, data_len + start_idx)
            if end_idx is not None and end_idx < 0:
                end_idx = max(0, data_len + end_idx)

            # 边界检查
            start_idx = max(0, min(start_idx, data_len - 1))
            if end_idx is not None:
                end_idx = max(start_idx, min(end_idx, data_len))
                return self.data.iloc[start_idx:end_idx]
            else:
                return self.data.iloc[start_idx:]

        except (IndexError, KeyError):
            return pd.DataFrame()

    def _pre_calculate_indicators(self):
        """预先计算整个数据集的常用技术指标"""
        try:
            # 确保数据类型正确，转换为float64
            close_data = self.data[self.close_column].astype(np.float64).values
            high_data = self.data[self.high_column].astype(np.float64).values
            low_data = self.data[self.low_column].astype(np.float64).values
            volume_data = self.data[self.volume_column].astype(np.float64).values

            # 检查数据有效性
            if len(close_data) < 30:  # 至少需要30个数据点（降低要求）
                return

            # 计算移动平均线
            self.data['ma5'] = ta.MA(close_data, timeperiod=5)
            self.data['ma10'] = ta.MA(close_data, timeperiod=10)
            self.data['ma20'] = ta.MA(close_data, timeperiod=20)
            self.data['ma30'] = ta.MA(close_data, timeperiod=30)
            self.data['ma60'] = ta.MA(close_data, timeperiod=60)

            # 计算其他技术指标
            self.data['atr14'] = ta.ATR(high_data, low_data, close_data, timeperiod=14)
            self.data['rsi14'] = ta.RSI(close_data, timeperiod=14)
            self.data['obv'] = ta.OBV(close_data, volume_data)

            # 计算MACD
            dif, dea, hist = ta.MACD(close_data, fastperiod=12, slowperiod=26, signalperiod=9)
            self.data['macd_dif'] = dif
            self.data['macd_dea'] = dea
            self.data['macd_hist'] = hist

        except Exception as e:
            # print(f"预计算指标失败: {e}")
            # 如果预计算失败，确保这些列存在但为空
            for col in ['ma5', 'ma10', 'ma20', 'ma30', 'ma60', 'atr14', 'rsi14', 'obv', 'macd_dif', 'macd_dea', 'macd_hist']:
                if col not in self.data.columns:
                    self.data[col] = np.nan

    def _cal_limit_num(self):
        """计算股票连板数量"""
        try:
            limit = 0
            if len(self.data) < 2: return 0
            # 涨停定义为收盘价相比前一收盘价上涨约10%（或20%），且收盘价等于最高价
            # 确保不会访问超出边界的索引
            max_check = min(10, len(self.data) - 1)  # 减1确保yesterday索引有效

            for i in range(1, max_check + 1):
                # 安全检查索引边界
                if i >= len(self.data) or (i + 1) > len(self.data):
                    break

                try:
                    today = self.data.iloc[-i]
                    yesterday = self.data.iloc[-(i + 1)]
                except IndexError:
                    break

                if yesterday[self.close_column] <= 0:  # 避免除零
                    break

                price_limit_ratio = today[self.close_column] / yesterday[self.close_column] - 1
                is_price_limit = 0.098 < price_limit_ratio < 0.102 or 0.198 < price_limit_ratio < 0.202
                if is_price_limit and today[self.close_column] == today[self.high_column]:
                    limit += 1
                else:
                    break
            return limit
        except Exception as e:
            # print(f"计算连板数失败: {e}")
            return 0

    def _cal_price_momentum(self, period):
        """计算指定周期的价格动量"""
        try:
            if len(self.data) < period + 1:
                return 0

            # 安全检查索引边界
            if period + 1 > len(self.data):
                return 0

            try:
                current_price = self.data[self.close_column].iloc[-1]
                past_price = self.data[self.close_column].iloc[-period-1]

                if past_price <= 0:  # 避免除零
                    return 0

                return (current_price / past_price) - 1
            except IndexError:
                return 0
        except Exception as e:
            # print(f"计算价格动量失败: {e}")
            return 0

    def _cal_bottom_return(self):
        """计算从底部到当前的收益率"""
        try:
            if len(self.data) < 100: return self._cal_price_momentum(len(self.data)-2)
            min_price = self.data[self.low_column].rolling(100).min().iloc[-1]
            if min_price == 0: return 0
            return (self.data[self.close_column].iloc[-1] - min_price) / min_price
        except Exception as e:
            # print(f"计算底部收益率失败: {e}")
            return 0

    def _cal_momentum(self):
        """判断股票动量趋势

        Returns:
            bool: 动量是否为正且短期动量大于长期动量
        """
        try:
            # 计算近10日和近20日收益率
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            # 动量为正且10日动量大于20日动量
            return ret10 > 0 and ret20 > 0 and ret10 > ret20
        except Exception as e:
            print(f"计算动量失败: {e}")
            return False

    def _cal_decision_date_return(self):
        """计算决策日（5天前）到当前的收益率

        Returns:
            float: 决策日收益率
        """
        try:
            # 假设决策日为5天前
            if len(self.data) < 5:
                return 0
            decision_date_price = self.data[self.close_column].iloc[-5]
            current_price = self.data[self.close_column].iloc[-1]
            return (current_price - decision_date_price) / decision_date_price
        except Exception as e:
            print(f"计算决策日收益率失败: {e}")
            return 0

    def _cal_isTup(self):
        """判断是否出现三连阳形态

        Returns:
            bool: 是否三连阳
        """
        try:
            if len(self.data) < 3:
                return False

            # 确保数据足够且有效
            if self.data.empty or len(self.data) < 3:
                return False

            # 安全地获取最近三天数据
            try:
                day1 = self.data.iloc[-1]  # 最近一天
                day2 = self.data.iloc[-2]  # 前一天
                day3 = self.data.iloc[-3]  # 前两天
            except IndexError as e:
                print(f"获取三连阳数据失败，索引错误: {e}")
                return False

            # 检查必要字段是否存在
            required_fields = ['close', 'open']
            for day in [day1, day2, day3]:
                for field in required_fields:
                    if field not in day or pd.isna(day[field]):
                        return False

            # 三连阳判断：连续三天收盘价高于开盘价
            return (day1['close'] > day1['open'] and
                   day2['close'] > day2['open'] and
                   day3['close'] > day3['open'])

        except Exception as e:
            print(f"计算三连阳失败: {e}")
            return False

    def _cal_emv(self):
        """计算简易版EMV（能量潮）指标

        Returns:
            bool: EMV是否大于其移动平均线且为正
        """
        try:
            # 简化版EMV计算
            if len(self.data) < 14:
                return False
            high = self.data[self.high_column]
            low = self.data[self.low_column]
            volume = self.data[self.volume_column]

            distance_move = (high + low) / 2 - (high.shift(1) + low.shift(1)) / 2
            box_ratio = volume / ((high - low) * 10000)
            emv = distance_move / box_ratio
            emv_ma = emv.rolling(14).mean()

            return emv.iloc[-1] > emv_ma.iloc[-1] and emv.iloc[-1] > 0
        except Exception as e:
            print(f"计算EMV失败: {e}")
            return False

    def _cal_k(self):
        """判断当前K线形态

        Returns:
            str: K线形态描述（大阳线、小阳线、大阴线、小阴线、十字星）
        """
        try:
            # 计算K线形态
            if len(self.data) < 1:
                return ''

            if self.data.empty:
                return ''

            # 安全地获取最新数据
            try:
                today = self.data.iloc[-1]
            except IndexError as e:
                print(f"获取K线数据失败，索引错误: {e}")
                return ''

            # 检查必要字段是否存在
            required_fields = ['close', 'open', 'high', 'low']
            for field in required_fields:
                if field not in today or pd.isna(today[field]):
                    return ''

            # 防止除零错误
            price_range = today['high'] - today['low']
            if price_range <= 0:
                return '十字星'

            # 简单分类K线形态
            if today['close'] > today['open']:
                body_ratio = (today['close'] - today['open']) / price_range
                if body_ratio > 0.6:
                    return '大阳线'
                else:
                    return '小阳线'
            elif today['close'] < today['open']:
                body_ratio = (today['open'] - today['close']) / price_range
                if body_ratio > 0.6:
                    return '大阴线'
                else:
                    return '小阴线'
            else:
                return '十字星'

        except Exception as e:
            print(f"计算K线形态失败: {e}")
            return ''

    def analyze_stock(self, code, df=None):
        """分析股票并返回StockOldAnalysis对象

        Args:
            code (str): 股票代码
            df (pd.DataFrame, optional): 股票数据DataFrame，如不提供则使用实例数据

        Returns:
            StockOldAnalysis: 包含股票分析结果的对象
        """
        """分析股票并返回StockOldAnalysis对象"""
        try:
            try:
                from .data_models import StockOldAnalysis
            except ImportError:
                from data_models import StockOldAnalysis

            if df is not None:
                self.data = df
                self._pre_calculate_indicators()

            if len(self.data) < 30:  # 降低数据量要求从60天到30天
                 # print(f"数据量不足，无法分析股票 {code}")
                 return StockOldAnalysis(
                     code=code,
                     limit=0,
                     ret10=0.0,
                     ret20=0.0,
                     ret100=0.0,
                     momentum=False,
                     isBottomInversion=False,
                     decisionPercent=0.0,
                     isTup=False,
                     isHeavyVolume=False,
                     macd=0,
                     kline="",
                     ret=0.0,
                     md=0.0,
                     alpha=0.0,
                     beta=1.0,
                     emv=False,
                     score1=0.0,
                     score2=0.0,
                     score3=0.0,
                     score4=0.0,
                     ma=False,
                     max30=False,
                     strongTrend=False,
                     breakoutPlatform=False,
                     bigDivergence=False,
                     fundAccumulation=False,
                     buySignal=False
                 )

            # 重新计算所有指标
            limit = self._cal_limit_num()
            isBottomInversion = self._cal_bottom_inversion()
            isHeavyVolume = self._cal_isHeavyVolume()
            capm_result = self._cal_CAPM()
            macd_signal = self._cal_macd() # MACD信号优化
            ma_signal = self._cal_ma() # MA信号优化
            max30 = self._cal_high_max()
            isTup = self._cal_isTup()
            emv = self._cal_emv()
            momentum = self._cal_momentum()
            decisionPercent = self._cal_decision_date_return()
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            ret100 = self._cal_price_momentum(100)
            kline = self._cal_k()

            # 新增四个核心量化信号
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()
            isFundAccumulation = self._cal_fund_accumulation()
            buySignal = self._cal_buy_signal()

            # 计算评分（这里假设需要实现评分逻辑）
            score1 = 0.0  # 使用量价强度作为第一个评分
            score2 = 0.0  # 示例值，需要根据实际逻辑计算
            score3 = 0.0  # 示例值，需要根据实际逻辑计算
            score4 = 0.0  # 示例值，需要根据实际逻辑计算

            # 创建并返回StockOldAnalysis对象
            return StockOldAnalysis(
                code=code,
                limit=limit,
                ret10=round(ret10, 4),
                ret20=round(ret20, 4),
                ret100=round(ret100, 4),
                momentum=momentum,
                isBottomInversion=isBottomInversion,
                decisionPercent=round(decisionPercent, 4),
                isTup=isTup,
                isHeavyVolume=isHeavyVolume,
                macd=macd_signal,
                kline=kline,
                ret=round(capm_result.get('ret', 0), 4),
                md=round(capm_result.get('max_drawdown', 0), 4),
                alpha=round(capm_result.get('alpha', 0), 4),
                beta=round(capm_result.get('beta', 1), 4),
                emv=emv,
                score1=score1,
                score2=score2,
                score3=score3,
                score4=score4,
                ma=ma_signal,
                max30=max30,
                strongTrend=isStrongTrend,
                breakoutPlatform=isBreakoutPlatform,
                bigDivergence=isBigDivergence,
                fundAccumulation=isFundAccumulation,
                buySignal=buySignal
            )
        except Exception as e:
            print(f"分析股票 {code} 失败: {e}")
            # 返回一个具有基本结构的StockOldAnalysis对象
            try:
                from .data_models import StockOldAnalysis
            except ImportError:
                from data_models import StockOldAnalysis
            return StockOldAnalysis(
                code=code,
                limit=0,
                ret10=0,
                ret20=0,
                ret100=0,
                momentum=False,
                isBottomInversion=False,
                decisionPercent=0,
                isTup=False,
                isHeavyVolume=False,
                macd=0,
                kline='',
                ret=0,
                md=0,
                alpha=0,
                beta=1,
                emv=False,
                score1=0,
                score2=0,
                score3=0,
                score4=0,
                ma=False,
                max30=False,
                strongTrend=False,
                breakoutPlatform=False,
                bigDivergence=False,
                fundAccumulation=False,
                buySignal=False
            )
    def _cal_isHeavyVolume(self):
        """判断是否出现放量突破

        Returns:
            bool: 是否放量突破
        """
        try:
            # 实体占比
            body_ratio = np.abs(self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 涨幅
            price_change = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9)
            # 价格位置
            price_position = (self.data[self.close_column] - self.data[self.low_column]) / (
                self.data[self.high_column] - self.data[self.low_column] + 1e-9)
            # 突破阻力
            resistance_break = self.data[self.close_column] > self.data[self.high_column].shift(1)
            
            # 综合判断
            is_heavy = ((body_ratio > 0.6) & 
                        (price_change > 0.03) & 
                        (price_position > 0.7) & 
                        resistance_break & 
                        (self.data[self.volume_column] > 1.5 * self.data[self.volume_column].rolling(20).mean())).iloc[-1]
            return bool(is_heavy)
        except Exception as e:
            print(f"计算放量突破失败: {e}")
            return False

    def _cal_bottom_inversion(self):
        """判断是否出现底部反转信号

        Returns:
            bool: 是否底部反转
        """
        try:
            # 持续下跌判断 - 增加连续下跌天数要求
            consecutive_drop = 0
            max_consecutive_check = min(15, len(self.data) - 1)  # 减1确保不越界
            if max_consecutive_check > 1:
                for i in range(1, max_consecutive_check):
                    # 安全检查索引边界
                    if i >= len(self.data) or (i + 1) > len(self.data):
                        break
                    try:
                        current = self.data[self.close_column].iloc[-i]
                        previous = self.data[self.close_column].iloc[-i-1]
                        if current < previous:
                            consecutive_drop += 1
                        else:
                            break
                    except IndexError:
                        break

            # 放量大阳线
            big_candle = (self.data[self.close_column] - self.data[self.open_column]) / (
                self.data[self.open_column] + 1e-9) > 0.05
            big_candle = big_candle.dropna()
            # 成交量放大
            volume_increase = self.data[self.volume_column] > 2.0 * self.data[self.volume_column].rolling(20).mean()
            volume_increase = volume_increase.dropna()
            # 均线信号
            ma5 = self.data[self.close_column].rolling(5).mean()
            ma10 = self.data[self.close_column].rolling(10).mean()
            # 对齐索引
            ma5, ma10 = ma5.align(ma10, join='inner')
            ma_cross = (ma5 > ma10) & (ma5.shift(1) <= ma10.shift(1))
            ma_cross = ma_cross.dropna()
            # RSI回升
            rsi = ta.RSI(self.data[self.close_column].values, timeperiod=14)
            rsi_rebound = False
            if len(rsi) >= 3:
                rsi_rebound = (rsi[-1] > rsi[-2]) & (rsi[-2] < rsi[-3]) & (rsi[-1] < 50)
            
            # 综合判断
            bottom_inversion = (consecutive_drop > 2)
            # 确保在访问iloc[-1]前有数据
            if len(big_candle) > 0:
                bottom_inversion &= big_candle.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有大阳线数据，不能确认底部反转
            
            if len(volume_increase) > 0:
                bottom_inversion &= volume_increase.iloc[-1]
            else:
                bottom_inversion = False  # 如果没有成交量数据，不能确认底部反转
            
            # 增加均线交叉的严格要求
            if len(ma_cross) > 0:
                # 要求不仅金叉，还要ma5在ma10之上有一定幅度
                ma_cross_strength = (ma5.iloc[-1] - ma10.iloc[-1]) / ma10.iloc[-1] > 0.01
                bottom_inversion &= (ma_cross.iloc[-1] & ma_cross_strength)
            else:
                bottom_inversion = False
            
            # 保持RSI回升条件但作为附加条件
            bottom_inversion &= rsi_rebound
            
            return bool(bottom_inversion)
        except Exception as e:
            print(f"计算底部反转失败: {e}")
            return False

    def _max_drawdown(self):
        """计算最大回撤

        Returns:
            float: 最大回撤值
        """
        try:
            cumulative_return = (1 + self.data['daily_return']).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()
            return max_drawdown
        except Exception as e:
            print(f"计算最大回撤失败: {e}")
            return 0

    def _cal_high_max(self):
        """判断收盘价是否接近最高价

        Returns:
            bool: 收盘价是否接近最高价
        """
        try:
            # 检查收盘价是否接近最高价
            high_close_ratio = self.data[self.close_column] / self.data[self.high_column]
            # 确保只使用有值的行
            high_close_ratio = high_close_ratio.dropna()
            if len(high_close_ratio) == 0:
                return False
            is_high_max = (high_close_ratio > 0.98).iloc[-1]
            return bool(is_high_max)
        except Exception as e:
            print(f"计算高价最大值失败: {e}")
            return False

    def _cal_CAPM(self):
        """计算CAPM模型相关指标

        Returns:
            dict: 包含收益率、alpha、beta和最大回撤的字典
        """
        try:
            if len(self.data) < 30:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算收益率
            returns = np.log(self.data[self.close_column] / self.data[self.close_column].shift(1))
            returns = returns.dropna()

            # 假设市场收益率为自身收益率（实际应用中应使用大盘指数）
            market_returns = returns.copy()

            if len(returns) < 10:
                return {'ret': 0, 'alpha': 0, 'beta': 1, 'max_drawdown': 0}

            # 计算beta和alpha
            market_returns_add = sm.add_constant(market_returns)  # 增加常数列
            model = sm.OLS(endog=returns, exog=market_returns_add)  # 计算线性回归模型
            result = model.fit()  # 拟合

            # 计算平均日收益率
            ret = returns.mean()

            # 计算最大回撤
            cumulative_return = (1 + returns).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()

            # 年化alpha
            alpha = round(result.params.iloc[0] * 250, 4)
            beta = round(result.params.iloc[1], 4)

            return {
                'ret': ret,
                'alpha': alpha,
                'beta': beta,
                'max_drawdown': max_drawdown
            }
        except Exception as e:
            print(f"CAPM计算失败: {e}")
            return {
                'ret': 0,
                'alpha': 0,
                'beta': 1,
                'max_drawdown': 0
            }

    def _cal_macd(self):
        """
        [优化] 计算MACD信号.
        返回值：
        - 1: 金叉买点或底背离买点
        - -1: 死叉卖点或顶背离卖点
        - 0: 无明确信号
        """
        try:
            if len(self.data) < 30:  # 降低数据量要求
                return 0

            # 如果预计算的MACD不存在，则现场计算
            if 'macd_dif' not in self.data.columns or self.data['macd_dif'].isna().all():
                close_data = self.data[self.close_column].astype(np.float64).values
                dif, dea, hist = ta.MACD(close_data, fastperiod=12, slowperiod=26, signalperiod=9)
                self.data['macd_dif'] = dif
                self.data['macd_dea'] = dea
                self.data['macd_hist'] = hist

            # 获取最近的数据
            last = self.data.iloc[-1]
            prev = self.data.iloc[-2]

            dif_last, dea_last, hist_last = last['macd_dif'], last['macd_dea'], last['macd_hist']
            dif_prev, dea_prev = prev['macd_dif'], prev['macd_dea']

            # 1. 检测背离信号（优先级最高）
            divergence_signal = self._detect_macd_divergence()
            if divergence_signal != 0:
                return divergence_signal

            # 2. 检测金叉死叉信号
            # 金叉：DIF由下向上穿过DEA
            is_golden_cross = dif_prev <= dea_prev and dif_last > dea_last
            # 死叉：DIF由上向下穿过DEA
            is_death_cross = dif_prev >= dea_prev and dif_last < dea_last

            if is_golden_cross:
                # 确认成交量 - 金叉日成交量 > 5日均量
                vol_last = last[self.volume_column]
                vol_ma5 = self.data[self.volume_column].rolling(5).mean().iloc[-1]
                if vol_last > vol_ma5 and dea_last < 0.1:  # 发生在0轴下方或附近
                    return 1
            elif is_death_cross:
                # 死叉信号
                return -1

            return 0
        except Exception as e:
            # print(f"MACD计算失败: {e}")
            return 0

    def _detect_macd_divergence(self):
        """检测MACD背离信号"""
        try:
            if len(self.data) < 30:
                return 0

            # 获取最近30天的数据（安全地）
            recent_days = min(30, len(self.data))
            recent_data = self.data.iloc[-recent_days:]
            close_data = recent_data[self.close_column].values
            macd_data = recent_data['macd_dif'].values

            # 去除NaN值
            valid_indices = ~(np.isnan(close_data) | np.isnan(macd_data))
            if np.sum(valid_indices) < 10:
                return 0

            close_clean = close_data[valid_indices]
            macd_clean = macd_data[valid_indices]

            if len(close_clean) < 10:
                return 0

            # 检测底背离：价格创新低但MACD未创新低
            recent_close = close_clean[-10:]
            recent_macd = macd_clean[-10:]

            # 价格创新低但MACD未创新低（底背离）
            if (recent_close[-1] < recent_close[:-1].min() and
                recent_macd[-1] > recent_macd[:-1].min()):
                return 1

            # 价格创新高但MACD未创新高（顶背离）
            elif (recent_close[-1] > recent_close[:-1].max() and
                  recent_macd[-1] < recent_macd[:-1].max()):
                return -1

            return 0

        except Exception as e:
            # print(f"检测MACD背离失败: {e}")
            return 0

    def _cal_ma(self):
        """
        [优化] 计算均线信号.
        判断是否形成稳定、清晰的多头排列趋势，并刚刚进入可买入区域。
        """
        try:
            if len(self.data) < 20:  # 降低数据量要求
                return False

            # 如果预计算的均线不存在，则现场计算
            if 'ma5' not in self.data.columns or self.data['ma5'].isna().all():
                close_data = self.data[self.close_column].astype(np.float64).values
                self.data['ma5'] = ta.MA(close_data, timeperiod=5)
                self.data['ma10'] = ta.MA(close_data, timeperiod=10)
                self.data['ma20'] = ta.MA(close_data, timeperiod=20)
                self.data['ma60'] = ta.MA(close_data, timeperiod=60)

            last = self.data.iloc[-1]
            prev = self.data.iloc[-2]

            # 条件1: 均线多头排列 (硬性条件)
            is_bullish_arrangement = last['ma5'] > last['ma10'] and last['ma10'] > last['ma20'] and last['ma20'] > last['ma60']
            if not is_bullish_arrangement:
                return False

            # 条件2: 均线斜率健康向上
            # 使用线性回归计算最近10天MA10的斜率，确保趋势稳定
            recent_days = min(10, len(self.data))
            y = self.data['ma10'].iloc[-recent_days:].values
            x = np.arange(len(y))
            if len(y) < 2:  # 至少需要2个点才能计算斜率
                return False
            slope = np.polyfit(x, y, 1)[0]
            if slope <= 0: # 斜率必须为正
                return False

            # 条件3: 价格在均线上方，但并未过度偏离 (寻找回调买点)
            # 价格在MA10之上，且刚刚从MA10附近回升
            is_above_ma10 = last[self.close_column] > last['ma10']
            # 前几天可能触及或接近MA10
            recent_days = min(5, len(self.data))
            was_near_ma10 = (self.data[self.low_column].iloc[-recent_days:] <= self.data['ma10'].iloc[-recent_days:] * 1.01).any()

            # 乖离率不能过大，防止追高
            bias = (last[self.close_column] / last['ma20'] - 1) * 100
            if bias > 15: # 距离20日线超过15%则过高
                return False

            # 条件4: 成交量配合 - 最近上涨日成交量 > 最近下跌日成交量
            recent_days = min(5, len(self.data))
            recent_data = self.data.iloc[-recent_days:]
            up_days_vol = recent_data[recent_data[self.close_column] > recent_data[self.open_column]][self.volume_column].mean()
            down_days_vol = recent_data[recent_data[self.close_column] < recent_data[self.open_column]][self.volume_column].mean()

            # 填充NaN防止错误
            up_days_vol = up_days_vol if pd.notna(up_days_vol) else 0
            down_days_vol = down_days_vol if pd.notna(down_days_vol) else 1 # 设为1防止除0

            volume_healthy = up_days_vol > down_days_vol * 1.2

            return is_bullish_arrangement and slope > 0 and is_above_ma10 and was_near_ma10 and volume_healthy

        except Exception as e:
            # print(f"计算均线信号失败: {e}")
            return False
            
    def _cal_strong_trend(self):
        """
        [核心优化] 计算强势趋势指标.
        一个股票被认为是强势趋势，必须满足均线多头排列，趋势稳定，且量价关系健康。
        """
        try:
            if len(self.data) < 30: return False  # 降低数据量要求

            # 确保均线数据存在
            if 'ma5' not in self.data.columns or self.data['ma5'].isna().all():
                close_data = self.data[self.close_column].astype(np.float64).values
                self.data['ma5'] = ta.MA(close_data, timeperiod=5)
                self.data['ma10'] = ta.MA(close_data, timeperiod=10)
                self.data['ma20'] = ta.MA(close_data, timeperiod=20)
                self.data['ma60'] = ta.MA(close_data, timeperiod=60)

            last = self.data.iloc[-1]

            # 1. 均线系统呈"多头排列"
            ma_bullish = last['ma5'] > last['ma10'] > last['ma20'] > last['ma60']
            if not ma_bullish: return False

            # 2. 均线斜率稳定向上 (检查多条均线)
            for period in [5, 10, 20]:
                recent_days = min(10, len(self.data))
                y = self.data[f'ma{period}'].iloc[-recent_days:].values
                x = np.arange(len(y))
                if len(y) < 2:  # 至少需要2个点才能计算斜率
                    return False
                if np.polyfit(x, y, 1)[0] <= 0:
                    return False

            # 3. 价格沿MA5或MA10健康上行
            price_on_ma = last[self.close_column] > last['ma5']
            # 最近10天，价格跌破MA10的天数不能超过2天
            recent_days = min(10, len(self.data))
            break_ma10_days = (self.data[self.close_column].iloc[-recent_days:] < self.data['ma10'].iloc[-recent_days:]).sum()
            if not price_on_ma or break_ma10_days > 2:
                return False

            # 4. 波动率(ATR)受控，不能是失控的疯涨
            if 'atr14' not in self.data.columns or self.data['atr14'].isna().all():
                high_data = self.data[self.high_column].astype(np.float64).values
                low_data = self.data[self.low_column].astype(np.float64).values
                close_data = self.data[self.close_column].astype(np.float64).values
                self.data['atr14'] = ta.ATR(high_data, low_data, close_data, timeperiod=14)

            atr_ratio = last['atr14'] / last[self.close_column]
            if atr_ratio > 0.07: # 日均波动超过7%可能过热
                return False

            # 5. 量价关系健康: 上涨放量，下跌缩量
            # 最近20天，上涨日的平均成交量 > 下跌日的平均成交量
            recent_days = min(20, len(self.data))
            recent_data = self.data.iloc[-recent_days:].copy()
            recent_data['change'] = recent_data[self.close_column].diff()
            avg_vol_up = recent_data[recent_data['change'] > 0][self.volume_column].mean()
            avg_vol_down = recent_data[recent_data['change'] <= 0][self.volume_column].mean()
            if not (pd.notna(avg_vol_up) and pd.notna(avg_vol_down) and avg_vol_up > avg_vol_down * 1.2):
                return False

            return True
        except Exception as e:
            # print(f"计算强势趋势股失败: {e}")
            return False
            
    def _cal_breakout_platform(self):
        """
        [核心优化] 计算平台突破指标.
        识别一个明确的、长期的盘整平台，并捕捉到带量的有效突破。
        """
        try:
            if len(self.data) < 60: return False

            # 1. 寻找一个长为30-60天的盘整平台
            platform_period = min(40, len(self.data) - 10)  # 确保有足够的数据
            if platform_period < 30:
                return False

            # 安全地获取平台数据
            start_idx = max(0, len(self.data) - platform_period - 5)
            end_idx = max(start_idx + 1, len(self.data) - 5)
            platform_data = self.data.iloc[start_idx:end_idx]
            if len(platform_data) < 30:
                return False

            platform_high = platform_data[self.high_column].max()
            platform_low = platform_data[self.low_column].min()
            platform_vol_avg = platform_data[self.volume_column].mean()

            # 2. 平台振幅必须足够小
            if (platform_high - platform_low) / platform_low > 0.20: # 振幅大于20%不算平台
                return False

            # 3. 寻找突破信号 (最近5天内发生)
            recent_days = min(5, len(self.data))
            breakout_data = self.data.iloc[-recent_days:]

            # 条件a: 收盘价有效突破平台高点
            breakout_day = None
            breakout_day_index = None
            for i in range(1, recent_days + 1):
                if i > len(self.data):
                    break
                day_to_check = self._safe_iloc(-i)
                if day_to_check is None:
                    continue
                if day_to_check[self.close_column] > platform_high * 1.01: # 收盘价高出1%
                    breakout_day_index = -i
                    breakout_day = day_to_check
                    break
            if breakout_day is None:
                return False

            # 条件b: 突破日成交量显著放大
            if breakout_day[self.volume_column] < platform_vol_avg * 1.8: # 要求成交量放大1.8倍
                return False

            # 条件c: 突破后没有立即跌回
            # 突破日之后的所有日子，最低价不能低于平台高点
            if breakout_day_index is not None:
                # 安全地获取突破后的数据
                after_breakout_start = len(self.data) + breakout_day_index + 1
                if after_breakout_start < len(self.data):
                    data_after_breakout = self.data.iloc[after_breakout_start:]
                    if not data_after_breakout.empty:
                        if (data_after_breakout[self.low_column] < platform_high).any():
                            return False

            # 条件d: 当前价格仍维持在突破位之上
            if self.data[self.close_column].iloc[-1] < platform_high:
                return False

            return True
        except Exception as e:
            # print(f"计算平台突破失败: {e}")
            return False
            
    def _cal_big_divergence(self):
        """
        [核心优化] 计算大分歧形态.
        严格定义为在高位或低位的转折点，伴随巨量和长影线。
        """
        try:
            if len(self.data) < 60: return False

            day1 = self.data.iloc[-1]
            day2 = self.data.iloc[-2]

            # 1. 计算影线和实体
            def get_k_features(day):
                body = abs(day[self.close_column] - day[self.open_column])
                upper_shadow = day[self.high_column] - max(day[self.close_column], day[self.open_column])
                lower_shadow = min(day[self.close_column], day[self.open_column]) - day[self.low_column]
                total_range = day[self.high_column] - day[self.low_column]
                return body, upper_shadow, lower_shadow, total_range

            body1, upper1, lower1, range1 = get_k_features(day1)
            body2, upper2, lower2, range2 = get_k_features(day2)

            if range1 == 0 or range2 == 0: return False

            # 2. 形态定义：一天长上影，一天长下影
            long_upper_ratio, long_lower_ratio = 0.4, 0.4 # 影线占总振幅40%以上
            pattern1 = (upper2 / range2 > long_upper_ratio) and (lower1 / range1 > long_lower_ratio) # 前天上影，今天下影
            pattern2 = (lower2 / range2 > long_lower_ratio) and (upper1 / range1 > long_upper_ratio) # 前天下影，今天上影

            if not (pattern1 or pattern2): return False

            # 3. 位置确认：必须发生在阶段性高位或低位
            # 安全地获取历史数据，避免索引越界
            period_length = min(60, len(self.data))
            period_data = self.data.iloc[-period_length:]
            high_60 = period_data[self.high_column].max()
            low_60 = period_data[self.low_column].min()
            current_high = max(day1[self.high_column], day2[self.high_column])
            current_low = min(day1[self.low_column], day2[self.low_column])

            # 计算价格分位
            price_percentile = (current_high - low_60) / (high_60 - low_60)
            is_at_top = price_percentile > 0.85 # 发生在85%以上的高位
            is_at_bottom = price_percentile < 0.15 # 发生在15%以下的低位

            if not (is_at_top or is_at_bottom): return False

            # 4. 成交量确认：这两天必须是巨量
            vol_period = min(20, len(self.data) - 2)
            if vol_period < 5:  # 至少需要5天的数据来计算平均值
                return False
            vol_avg_20 = self.data[self.volume_column].iloc[-vol_period-2:-2].mean()
            vol_day1 = day1[self.volume_column]
            vol_day2 = day2[self.volume_column]

            if not (vol_day1 > vol_avg_20 * 2 and vol_day2 > vol_avg_20 * 2): # 两天成交量都是20日均量的2倍以上
                return False

            # 如果是底部大分歧，则是一个潜在买点
            if is_at_bottom:
                return True
            else: # 顶部大分歧是卖点
                return False

        except Exception as e:
            # print(f"计算大分歧失败: {e}")
            return False
            
    def _cal_fund_accumulation(self):
        """
        [核心优化] 计算资金吸筹指标.
        核心逻辑: 长期底部横盘，波动率持续下降，但OBV（能量潮）指标却在悄悄走高，形成"价平量升"的背离。
        """
        try:
            if len(self.data) < 120 or 'obv' not in self.data.columns: return False

            # 1. 寻找长达60-90天的底部盘整区
            period = min(90, len(self.data))
            if period < 60:  # 至少需要60天的数据
                return False
            data_period = self.data.iloc[-period:]

            # 条件a: 价格处于一年内的相对低位
            low_250 = self.data[self.low_column].rolling(250).min().iloc[-1]
            if self.data[self.close_column].iloc[-1] > low_250 * 1.5: # 当前价格不能比年内低点高出50%
                return False

            # 条件b: 近期波动率持续萎缩
            # 布林带宽度是衡量波动率的好指标
            upper, middle, lower = ta.BBANDS(data_period[self.close_column], timeperiod=20)
            bbw = (upper - lower) / middle
            # 要求当前布林带宽度处于90天内的最低1/3水平
            if bbw.iloc[-1] > bbw.quantile(0.33):
                return False

            # 2. 核心信号：OBV与价格发生底背离
            # 价格在盘整区内可能创新低或走平，但OBV却要创出盘整区的新高
            price_trend_slope = np.polyfit(np.arange(period), data_period[self.close_column], 1)[0]
            obv_trend_slope = np.polyfit(np.arange(period), data_period['obv'], 1)[0]

            # 价格趋势平缓或向下，但OBV趋势明显向上
            if price_trend_slope < 0.05 and obv_trend_slope > 0:
                # 进一步确认OBV斜率显著大于价格斜率
                # 简单比较即可，因为我们已经限定了价格斜率很小
                return True

            return False
        except Exception as e:
            # print(f"计算资金吸筹失败: {e}")
            return False

    def _cal_buy_signal(self):
        """
        [优化] 计算最终买入信号.
        买点逻辑更加清晰:
        - 强势趋势中的回调买点 (isStrongTrend为True)
        - 资金吸筹后的平台突破买点 (isFundAccumulation 和 isBreakoutPlatform 同时或先后出现)
        - 底部大分歧反转买点 (isBigDivergence为True)
        """
        try:
            # 获取四个量化指标的值
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()
            isFundAccumulation = self._cal_fund_accumulation()

            # 1. 最强的信号: 吸筹后的突破
            # 如果近期（30天内）曾识别出吸筹，现在又出现平台突破，这是最经典的买点
            if isBreakoutPlatform:
                # 简单回溯检查前30天是否有吸筹迹象（此处为简化，实际可做的更复杂）
                # 为了不改变结构，这里我们只判断当下的信号
                if isFundAccumulation: # 如果突破时仍能检测到吸筹特征，信号极强
                    return True
                # 如果不能，但突破本身成立，也是一个强信号
                return True

            # 2. 趋势跟踪买点
            # 处于强势趋势中，是右侧交易者的首选
            if isStrongTrend:
                return True

            # 3. 左侧反转买点
            # 识别出底部大分歧，适合愿意承担风险的左侧交易者
            if isBigDivergence:
                return True

            # 将原有的 ">=2" 逻辑作为补充
            conditions_met = sum([isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation])
            if conditions_met >= 2:
                return True

            return False
        except Exception as e:
            # print(f"计算买入信号失败: {e}")
            return False